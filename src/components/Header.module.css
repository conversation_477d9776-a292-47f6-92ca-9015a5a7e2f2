/* Header Component CSS Module */

.headerSpacer {
  height: var(--header-height);
}

.header {
  position: fixed;
  top: 0;
  right: 0;
  height: var(--header-height);
  background: #ffffff0f;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-bottom: 1px solid #ffffff00;
  transition: all var(--transition-base);
  z-index: 1040;
  width: 750px;
  margin: 20px auto;
  padding: 0;
  border-radius: 200px;
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: between;
  height: 100%;
  padding: 0 var(--spacing-4) 0 var(--spacing-8);
}

.headerBrand {
  display: flex;
  align-items: center;
  flex: 1;
}

.headerBrandTitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  line-height: var(--line-height-tight);
}

.headerBrandSubtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  margin-top: var(--spacing-0-5);
}

.headerActions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.headerUserStatus {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.headerUserIndicator {
  width: 8px;
  height: 8px;
  background-color: var(--color-success-500);
  border-radius: var(--radius-full);
}

.headerUserName {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.headerAiButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--radius-full);
  color: var(--color-primary-600);
  transition: all var(--transition-base);
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.headerAiButton:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.headerAiButton:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* AI Tooltip Styles */
.aiTooltipOverlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  z-index: 40;
}

.aiTooltip {
  position: fixed;
  width: 320px;
  z-index: 50;
}

.aiTooltipContent {
  background: var(--color-neumorphic-bg);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-neumorphic-elevated);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.aiTooltipHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.aiTooltipCloseButton {
  padding: var(--spacing-1);
  background: none;
  border: none;
  border-radius: var(--radius-full);
  color: var(--color-text-muted);
  cursor: pointer;
  transition: background-color var(--transition-base);
}

.aiTooltipCloseButton:hover {
  background: var(--color-gray-100);
}

.aiTooltipContent h3 {
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-300);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
}

.aiTooltipText {
  color: var(--color-gray-100);
  line-height: var(--line-height-relaxed);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

.aiTooltipActions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.aiTooltipButton {
  padding: var(--spacing-2) var(--spacing-4);
  background: var(--color-primary-600);
  color: white;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-lg);
}

.aiTooltipButton:hover {
  background: var(--color-primary-700);
  box-shadow: var(--shadow-xl);
}

.aiTooltipArrow {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--color-neumorphic-bg);
  transform: rotate(45deg);
  box-shadow: var(--shadow-lg);
  top: 0;
  margin-top: -6px;
  right: var(--spacing-4);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .headerContent {
    padding: 0 var(--spacing-4);
  }
  
  .headerUserName {
    display: none;
  }
  
  .aiTooltip {
    width: 280px;
  }
}