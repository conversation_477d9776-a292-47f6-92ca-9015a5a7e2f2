import React, { useState, useId } from "react";
import { Check, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import { useScrollManager } from "../utils/scrollManager";

const timelineEvents = [
  {
    phase: "Study Design & Participant Recruit",
    date: "Jun 05, 2025",
    completed: true,
    details: [
      "Research methodology defined",
      "Participant criteria established",
      "30 potential participants identified",
    ],
  },
  {
    phase: "Phase I: Real Life",
    date: "Jun 07, 2025",
    completed: true,
    details: [
      "In-depth interviews conducted",
      "Daily activity logs collected",
      "Behavioral patterns analyzed",
    ],
  },
  {
    phase: "Phase II: Holiday Prep",
    date: "Jun 09, 2025",
    completed: true,
    details: [
      "Shopping patterns documented",
      "Decision-making processes analyzed",
      "Pain points identified",
    ],
  },
  {
    phase: "Phase III: Holiday Experience",
    date: "Jun 11, 2025",
    completed: true,
    details: [
      "Experience mapping completed",
      "Key moments identified",
      "Satisfaction metrics collected",
    ],
  },
  {
    phase: "Phase IV: Depth Interviews",
    date: "Jun 12, 2025",
    completed: true,
    details: [
      "Follow-up interviews conducted",
      "Data synthesis completed",
      "Initial insights generated",
    ],
  },
  {
    phase: "Persona Workshop",
    date: "Today",
    current: true,
    details: [
      "Persona attributes definition",
      "Journey mapping",
      "Validation session",
    ],
  },
  {
    phase: "Completion",
    date: "Jun 15, 2025",
    upcoming: true,
    details: [
      "Final report compilation",
      "Presentation preparation",
      "Stakeholder review",
    ],
  },
];

export function Timeline() {
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  const firstRow = timelineEvents.slice(0, 4);
  const secondRow = timelineEvents.slice(4);
  
  // Use the scroll manager to preserve scroll position (Timeline modal is detected as regular)
  useScrollManager(modalId, selectedEvent !== null, 'Timeline');
  
  // const completedCount = timelineEvents.filter(te => te.completed).length;
  // const progressPercentage = (completedCount / timelineEvents.length) * 100;

  return (
    <div className='space-y-8 p-2'>
      <div>
        <h2 className='text-3xl font-bold text-text-primary'>
          Research Timeline
        </h2>
        <p className='text-text-secondary mt-2 text-lg'>
          Project phases and milestones
        </p>
      </div>

      <NeumorphicContainer>
        <div className='flex flex-col space-y-16'>
          {/* Progress indicator */}
          {/* <div className="flex items-center gap-2">
            <div className="h-2 flex-1 rounded-full bg-gray-100">
              <div 
                className="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-500"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            <span className="text-sm font-medium text-gray-600">
              {Math.round(progressPercentage)}%
            </span>
          </div> */}

          {/* First row - Left to right */}
          <div className='flex items-center px-12 pt-16'>
            {firstRow.map((event, index) => (
              <React.Fragment key={event.phase}>
                <motion.div
                  className='relative'
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center relative z-10
                    ${
                      event.completed
                        ? "bg-gradient-to-r from-green-400 to-green-500 shadow-lg shadow-green-100"
                        : event.current
                        ? "bg-gradient-to-r from-blue-400 to-blue-500 shadow-lg shadow-blue-100"
                        : "bg-white border-2 border-gray-300"
                    }`}
                  >
                    {event.completed && (
                      <Check className='h-4 w-4 text-white' />
                    )}
                    {event.current && (
                      <div className='w-2 h-2 rounded-full bg-white' />
                    )}
                  </div>
                  <button
                    onClick={() => setSelectedEvent(index)}
                    className='absolute -top-16 left-1/2 -translate-x-1/2 w-32 text-center'
                  >
                    <p className='text-sm font-medium text-text-primary hover:text-text-accent transition-colors'>
                      {event.phase}
                    </p>
                    <p className='text-xs text-text-muted mt-1'>{event.date}</p>
                  </button>
                </motion.div>
                {index < firstRow.length - 1 && (
                  <div className='h-[3px] bg-gradient-to-r from-green-400 to-green-500 flex-1' />
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Second row - Right to left */}
          <div className='flex flex-row-reverse items-center relative px-12 pb-16'>
            <div className='absolute right-[59px] -top-[75px] w-[3px] bg-gradient-to-b from-green-500 to-blue-500 h-[75px]' />
            {secondRow.map((event, index) => (
              <React.Fragment key={event.phase}>
                <motion.div
                  className='relative'
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0, y: 0 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: (index + firstRow.length) * 0.1 }}
                >
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center relative z-10
                    ${
                      event.completed
                        ? "bg-gradient-to-r from-green-400 to-green-500 shadow-lg shadow-green-100"
                        : event.current
                        ? "bg-gradient-to-r from-blue-400 to-blue-500 shadow-lg shadow-blue-100"
                        : "bg-white border-2 border-gray-300"
                    }`}
                  >
                    {event.completed && (
                      <Check className='h-4 w-4 text-white' />
                    )}
                    {event.current && (
                      <div className='w-2 h-2 rounded-full bg-white' />
                    )}
                  </div>
                  <button
                    onClick={() => setSelectedEvent(index + firstRow.length)}
                    className='absolute -bottom-16 left-1/2 -translate-x-1/2 w-32 text-center'
                  >
                    <p className='text-sm font-medium text-text-primary hover:text-text-accent transition-colors'>
                      {event.phase}
                    </p>
                    <p className='text-xs text-text-muted mt-1'>{event.date}</p>
                  </button>
                </motion.div>
                {index < secondRow.length - 1 && (
                  <div className='h-[3px] bg-gradient-to-r from-green-400 to-green-500 flex-1' />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </NeumorphicContainer>

      {/* Modal */}
      <AnimatePresence>
        {selectedEvent !== null && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center z-50'
            onClick={() => setSelectedEvent(null)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className='bg-white rounded-2xl shadow-2xl max-w-lg w-full mx-4 relative overflow-hidden'
            >
              <div className='p-8'>
                <button
                  onClick={() => setSelectedEvent(null)}
                  className='absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-lg transition-colors'
                >
                  <X className='h-5 w-5 text-text-muted' />
                </button>

                <h3 className='text-2xl font-bold text-text-primary mb-2'>
                  {timelineEvents[selectedEvent].phase}
                </h3>
                <p className='text-sm text-text-secondary mb-6'>
                  {timelineEvents[selectedEvent].date}
                </p>

                <div className='space-y-4'>
                  {timelineEvents[selectedEvent].details.map(
                    (detail, index) => (
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        key={index}
                        className='flex items-start'
                      >
                        <div className='h-2 w-2 rounded-full bg-blue-600 mt-2 mr-3' />
                        <span className='text-text-secondary'>{detail}</span>
                      </motion.div>
                    )
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
