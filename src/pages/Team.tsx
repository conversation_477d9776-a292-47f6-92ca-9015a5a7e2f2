import React, { useState, useId } from "react";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { FaXTwitter } from "react-icons/fa6";
import { BsSlack } from "react-icons/bs";
import { BsMicrosoftTeams } from "react-icons/bs";
import { IoCall } from "react-icons/io5";
import { MdEmail } from "react-icons/md";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";

const team = [
  {
    name: "<PERSON>",
    role: "Research Lead",
    imageUrl:
      "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80&w=200&h=200",
    email: "<EMAIL>",
    details:
      "<PERSON> oversees the research process, coordinates the team, and ensures the project meets its objectives. She is responsible for methodology and final reporting.",
  },
  {
    name: "<PERSON>",
    role: "Senior Analyst",
    imageUrl:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80&w=200&h=200",
    email: "<EMAIL>",
    details:
      "Michael analyzes collected data, identifies trends, and synthesizes insights. He is key in transforming raw data into actionable findings.",
  },
  {
    name: "Emily Rodriguez",
    role: "UX Researcher",
    imageUrl:
      "https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&q=80&w=200&h=200",
    email: "<EMAIL>",
    details:
      "Emily conducts interviews and usability tests, focusing on user experience and pain points. She helps ensure the personas are grounded in real user needs.",
  },
  {
    name: "David Kim",
    role: "Data Analyst",
    imageUrl:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&q=80&w=200&h=200",
    email: "<EMAIL>",
    details:
      "David manages data collection and visualization, and supports the team with statistical analysis and reporting.",
  },
];

export function Team() {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const modalId = useId(); // Generate unique ID for this modal instance

  // Register team modal with overlay system
  useOverlayState(selectedIndex !== null);
  
  // Use the scroll manager to preserve scroll position (Team modal is detected as regular)
  useScrollManager(modalId, selectedIndex !== null, 'Team');

  const openModal = (index: number) => setSelectedIndex(index);
  const closeModal = () => setSelectedIndex(null);
  const showPrev = () =>
    setSelectedIndex((prev) =>
      prev !== null ? (prev - 1 + team.length) % team.length : null
    );
  const showNext = () =>
    setSelectedIndex((prev) =>
      prev !== null ? (prev + 1) % team.length : null
    );

  return (
    <div className='space-y-8 p-2'>
      <div>
        <h2 className='text-3xl font-bold text-text-primary'>Research Team</h2>
        <p className='text-text-secondary mt-2 text-lg'>
          Meet the people behind the insights
        </p>
      </div>

      <NeumorphicContainer>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
          {team.map((member, index) => (
            <div
              key={index}
              className='neumorphic-elevated text-center transform transition-transform hover:scale-105 duration-200 cursor-pointer p-6'
              onClick={() => openModal(index)}
            >
              <div className='relative'>
                <img
                  src={member.imageUrl}
                  alt={member.name}
                  className='w-32 h-32 rounded-full mx-auto mb-4 shadow-lg'
                />
              </div>
              <h3 className='text-lg font-semibold text-text-primary'>
                {member.name}
              </h3>
              <p className='text-sm text-text-secondary'>{member.role}</p>
            </div>
          ))}
        </div>
      </NeumorphicContainer>

      {/* Modal for team member details with carousel */}
      {selectedIndex !== null && (
        <div className='modal-with-sidebar z-50 flex items-center justify-center p-4'>
          {/* Backdrop */}
          <div
            className='absolute inset-0 bg-black/75 backdrop-blur-sm'
            onClick={closeModal}
          />

          {/* Modal Container */}
          <div
            className='relative neumorphic-container max-w-md w-full min-h-[420px] h-[500px] mx-4 p-8 animate-fade-in flex flex-col items-center justify-center'
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={closeModal}
              className='absolute top-4 right-4 p-2 neumorphic-button-secondary rounded-lg'
              aria-label='Close'
            >
              <X className='h-5 w-5' />
            </button>
            <img
              src={team[selectedIndex].imageUrl}
              alt={team[selectedIndex].name}
              className='w-32 h-32 rounded-full mb-4 shadow-lg'
            />
            <h3 className='text-2xl font-bold text-text-primary mb-1'>
              {team[selectedIndex].name}
            </h3>
            <p className='text-lg text-text-secondary mb-2'>
              {team[selectedIndex].role}
            </p>
            <p className='text-text-secondary text-left mb-4 flex-1'>
              {team[selectedIndex].details}
            </p>
            <div className='flex items-center justify-between w-full mt-4'>
              <button
                onClick={showPrev}
                className='p-2 neumorphic-button-secondary rounded-full'
                aria-label='Previous'
              >
                <ChevronLeft className='h-6 w-6' />
              </button>

              {/* Contact Icons Container - positioned between navigation buttons */}
              <div className='flex items-center space-x-3'>
                {/* Email Icon */}
                <button
                  onClick={() => console.log("Email clicked - placeholder")}
                  className='p-2 neumorphic-button-secondary rounded-full transition-all duration-200 hover:scale-110 hover:shadow-lg group'
                  aria-label='Send Email'
                  title='Send Email'
                >
                  <MdEmail className='h-5 w-5 text-text-primary group-hover:text-text-secondary transition-colors' />
                </button>

                {/* X/Twitter Icon */}
                <button
                  onClick={() => console.log("X/Twitter clicked - placeholder")}
                  className='p-2 neumorphic-button-secondary rounded-full transition-all duration-200 hover:scale-110 hover:shadow-lg group'
                  aria-label='View X Profile'
                  title='View X Profile'
                >
                  <FaXTwitter className='h-5 w-5 text-text-primary group-hover:text-text-secondary transition-colors' />
                </button>

                {/* Slack Icon */}
                <button
                  onClick={() => console.log("Slack clicked - placeholder")}
                  className='p-2 neumorphic-button-secondary rounded-full transition-all duration-200 hover:scale-110 hover:shadow-lg group'
                  aria-label='Send Slack Message'
                  title='Send Slack Message'
                >
                  <BsSlack className='h-5 w-5 text-text-primary group-hover:text-text-secondary transition-colors' />
                </button>

                {/* Teams Icon */}
                <button
                  onClick={() => console.log("Teams clicked - placeholder")}
                  className='p-2 neumorphic-button-secondary rounded-full transition-all duration-200 hover:scale-110 hover:shadow-lg group'
                  aria-label='Send Teams Message'
                  title='Send Teams Message'
                >
                  <BsMicrosoftTeams className='h-5 w-5 text-text-primary group-hover:text-text-secondary transition-colors' />
                </button>

                {/* Phone Icon */}
                <button
                  onClick={() => console.log("Phone clicked - placeholder")}
                  className='p-2 neumorphic-button-secondary rounded-full transition-all duration-200 hover:scale-110 hover:shadow-lg group'
                  aria-label='Call Phone'
                  title='Call Phone'
                >
                  <IoCall className='h-5 w-5 text-text-primary group-hover:text-text-secondary transition-colors' />
                </button>
              </div>

              <button
                onClick={showNext}
                className='p-2 neumorphic-button-secondary rounded-full'
                aria-label='Next'
              >
                <ChevronRight className='h-6 w-6' />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
