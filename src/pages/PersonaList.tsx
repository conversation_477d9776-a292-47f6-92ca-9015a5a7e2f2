import React, { useState, useId } from "react";
import { User, Info, Lightbulb, X, ArrowRight, ArrowLeft } from "lucide-react";
import { PersonaDetail } from "./PersonaDetail"; // Import PersonaDetail component
import Slider from "react-slick";
import { PersonaCard } from "../components/PersonaCard";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import Masonry from "@mui/lab/Masonry";
import { PersonaInfo } from "../components/PersonaInfo";
import { mockPersonas, CATEGORY_DESCRIPTIONS } from "../data/mockPersonas";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";

import ambitious from "../assets/Ambitious.jpeg";
import architect from "../assets/Architect.jpeg";
import contented from "../assets/Contented.jpeg";
import sophisticate from "../assets/Sophisticate.jpeg";
import overcomer from "../assets/Overcomer.jpeg";

// Convert mockPersonas to the format expected by PersonaCard
const PERSONAS_FOR_CARDS = Object.values(mockPersonas).map((persona) => ({
  id: persona.id,
  name: persona.name,
  category: persona.category,
  title: persona.title,
  summary: persona.background, // Use background as summary
  profileImageUrl: persona.profileImageUrl,
  profileImageName: persona.profileImageName,
  description: CATEGORY_DESCRIPTIONS[persona.category]?.description || "",
}));

const categories = [
  "All",
  "Ambitious",
  "Architect",
  "Contented",
  "Sophisticate",
  "Overcomer",
  "Traditionalist",
  "Minimalist",
  "Over-Achiever",
  "Curious",
  "Creative",
];

// Custom arrow components for All Personas slider
function CustomPrevArrow(props: any) {
  const { className, style, onClick } = props;
  return (
    <button
      type='button'
      onClick={onClick}
      className='absolute -left-16 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow hover:bg-gray-200 transition-colors z-20 disabled:opacity-50 disabled:cursor-not-allowed'
      style={{ ...style, display: "block" }}
      aria-label='Previous Persona'
    >
      <ArrowLeft className='h-6 w-6 text-gray-400' />
    </button>
  );
}

function CustomNextArrow(props: any) {
  const { className, style, onClick } = props;
  return (
    <button
      type='button'
      onClick={onClick}
      className='absolute -right-16 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow hover:bg-gray-200 transition-colors z-20 disabled:opacity-50 disabled:cursor-not-allowed'
      style={{ ...style, display: "block" }}
      aria-label='Next Persona'
    >
      <ArrowRight className='h-6 w-6 text-gray-400' />
    </button>
  );
}

const sliderSettings = {
  dots: true,
  infinite: true,
  speed: 500,
  slidesToShow: 3, // Show 3 cards at a time
  slidesToScroll: 1,
  swipeToSlide: true, // Enable swipe to slide
  touchMove: true, // Enable touch move
  swipe: true, // Enable swipe
  draggable: true, // Enable dragging
  touchThreshold: 10, // Sensitivity for touch gestures
  arrows: true, // Show navigation arrows
  adaptiveHeight: false,
  centerMode: false,
  variableWidth: false,
  prevArrow: <CustomPrevArrow />,
  nextArrow: <CustomNextArrow />,
  appendDots: (dots: React.ReactNode) => (
    <div className='flex space-x-2 mt-6 justify-center'>{dots}</div>
  ),
  customPaging: (i: number) => (
    <button
      className='w-3 h-3 rounded-full focus:outline-none transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
      aria-label={`Go to persona ${i + 1}`}
      type='button'
    />
  ),
  responsive: [
    {
      breakpoint: 1024, // For tablets
      settings: {
        slidesToShow: 2,
        swipeToSlide: true,
        touchMove: true,
        swipe: true,
        draggable: true,
      },
    },
    {
      breakpoint: 600, // For mobile devices
      settings: {
        slidesToShow: 1,
        swipeToSlide: true,
        touchMove: true,
        swipe: true,
        draggable: true,
        arrows: false, // Hide arrows on mobile for cleaner UI
      },
    },
  ],
};

// Example tips data
const tips = [
  {
    title: "What is a persona?",
    content:
      "A persona is a fictional character created based on user research to represent different user types.",
  },
  {
    title: "Why use personas?",
    content:
      "Personas help teams understand user needs, experiences, behaviors, and goals.",
  },
  {
    title: "How are personas created?",
    content:
      "Personas are created from qualitative and quantitative user research.",
  },
  {
    title: "Personas in design",
    content: "Personas guide design decisions by keeping real users in mind.",
  },
  {
    title: "Personas in communication",
    content:
      "Personas help align teams on who the users are and what they need.",
  },
  {
    title: "Personas and empathy",
    content:
      "Personas foster empathy for users throughout the product development process.",
  },
  {
    title: "Personas and business",
    content:
      "Personas help prioritize features and improvements that matter most to users.",
  },
];

// Persona overview data using actual category descriptions
const personaOverviews = [
  {
    title: "Ambitious",
    description: CATEGORY_DESCRIPTIONS.Ambitious.description,
    image: ambitious,
  },
  {
    title: "Architect",
    description: CATEGORY_DESCRIPTIONS.Architect.description,
    image: architect,
  },
  {
    title: "Contented",
    description: CATEGORY_DESCRIPTIONS.Contented.description,
    image: contented,
  },
  {
    title: "Sophisticate",
    description: CATEGORY_DESCRIPTIONS.Sophisticate.description,
    image: sophisticate,
  },
  {
    title: "Overcomer",
    description: CATEGORY_DESCRIPTIONS.Overcomer.description,
    image: overcomer,
  },
  {
    title: "Traditionalist",
    description: CATEGORY_DESCRIPTIONS.Traditionalist.description,
    image: ambitious, // Using existing image as placeholder
  },
  {
    title: "Minimalist",
    description: CATEGORY_DESCRIPTIONS.Minimalist.description,
    image: contented, // Using existing image as placeholder
  },
  {
    title: "Over-Achiever",
    description: CATEGORY_DESCRIPTIONS["Over-Achiever"].description,
    image: sophisticate, // Using existing image as placeholder
  },
  {
    title: "Curious",
    description: CATEGORY_DESCRIPTIONS.Curious.description,
    image: architect, // Using existing image as placeholder
  },
  {
    title: "Creative",
    description: CATEGORY_DESCRIPTIONS.Creative.description,
    image: overcomer, // Using existing image as placeholder
  },
];

// Layout-only carousel for Customer Persona section
export function PersonaCarouselLayout({
  children,
  total,
  current,
  onPrev,
  onNext,
  onDotClick,
  personaCategories,
}) {
  // 3D carousel parameters
  const cardWidth = 700; // Increased from 500px for much wider selected persona
  const radius = 800; // Increased from 700px for better spacing
  const step = 360 / total; // degrees between cards

  // Calculate rotation angle for the carousel
  const rotation = -current * step;

  return (
    <div className='relative flex flex-col items-center py-12 w-full overflow-hidden'>
      {/* 3D Carousel Container */}
      <div
        className='relative mx-auto'
        style={{
          width: `${cardWidth}px`,
          height: "500px", // Reduced from 600px for slightly shorter height
          perspective: "1600px", // Increased perspective for better 3D effect
        }}
      >
        <div
          className='absolute top-0 left-0 w-full h-full'
          style={{
            transformStyle: "preserve-3d",
            transition: "transform 0.7s cubic-bezier(0.77,0,0.18,1)",
            transform: `translateZ(-${radius}px) rotateY(${rotation}deg)`,
          }}
        >
          {React.Children.map(children, (child, idx) => {
            // Calculate each card's angle
            const angle = idx * step;
            // Only render cards near the front for performance
            const visible =
              Math.abs(((idx - current + total) % total) - total) <= 2 ||
              Math.abs(idx - current) <= 2;

            const isSelected = idx === current;

            return (
              <div
                key={idx}
                className='absolute left-0 top-0 w-full h-full flex items-center justify-center'
                style={{
                  transform: `rotateY(${angle}deg) translateZ(${radius}px)`,
                  opacity: isSelected ? 1 : 0.2, // Reduced opacity for non-selected
                  filter: isSelected ? "none" : "blur(6px)", // Increased blur for non-selected
                  zIndex: isSelected ? 3 : 1, // Higher z-index for selected
                  pointerEvents: isSelected ? "auto" : "none",
                  transition:
                    "opacity 0.5s, filter 0.5s, z-index 0.5s, transform 0.7s cubic-bezier(0.77,0,0.18,1)",
                  display: visible ? "flex" : "none",
                }}
              >
                <div
                  className={`w-full h-full flex items-center justify-center ${
                    isSelected
                      ? "scale-110" // Make selected persona 10% bigger
                      : "scale-85" // Make non-selected personas smaller
                  }`}
                  style={{
                    transition: "transform 0.5s cubic-bezier(0.77,0,0.18,1)",
                  }}
                >
                  {isSelected ? (
                    // Glassy container for selected persona - much wider with dynamic color-coded gradient
                    <div
                      className={`w-full h-full flex flex-col items-center justify-center p-8 rounded-3xl persona-overview-card ${
                        personaCategories && personaCategories[idx]
                          ? personaCategories[idx]
                              .toLowerCase()
                              .replace(/\s+/g, "-")
                          : "ambitious"
                      }`}
                      style={{
                        minWidth: "650px", // Ensure minimum width for wide container
                      }}
                    >
                      {child}
                    </div>
                  ) : (
                    // Regular container for non-selected personas
                    <div className='w-full h-full flex flex-col items-center justify-center p-6 rounded-2xl'>
                      {child}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Navigation arrows (far left/right, vertically centered) */}
      <button
        onClick={onPrev}
        className='absolute left-8 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:bg-white transition-all duration-200 z-20'
        aria-label='Previous Persona'
        style={{
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        }}
      >
        <ArrowLeft className='h-6 w-6 text-gray-600' />
      </button>
      <button
        onClick={onNext}
        className='absolute right-8 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:bg-white transition-all duration-200 z-20'
        aria-label='Next Persona'
        style={{
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        }}
      >
        <ArrowRight className='h-6 w-6 text-gray-600' />
      </button>

      {/* Pagination dots (centered below) */}
      <div className='flex space-x-3 mt-8'>
        {Array.from({ length: total }).map((_, idx) => (
          <button
            key={idx}
            onClick={() => onDotClick(idx)}
            className={`w-4 h-4 rounded-full border-2 focus:outline-none transition-all duration-200 ${
              idx === current
                ? "bg-blue-600 border-blue-600 shadow-lg scale-110"
                : "bg-white/50 border-gray-300 hover:bg-white/80"
            }`}
            aria-label={`Go to persona ${idx + 1}`}
            style={{
              backdropFilter: "blur(10px)",
            }}
          />
        ))}
      </div>
    </div>
  );
}

export function PersonaList({
  onPersonaClick,
}: {
  onPersonaClick: (id: string) => void;
}) {
  const [selectedCategory, setSelectedCategory] = React.useState("All");

  // State for managing persona detail modal
  const [selectedPersonaId, setSelectedPersonaId] = React.useState<
    string | null
  >(null);
  const [currentTip, setCurrentTip] = useState(0);
  const [currentPersona, setCurrentPersona] = useState(0);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register persona modal with overlay system
  useOverlayState(selectedPersonaId !== null);
  
  // Use the scroll manager to preserve scroll position (PersonaList modal is detected as regular)
  useScrollManager(modalId, selectedPersonaId !== null, 'PersonaList');

  const filteredPersonas = PERSONAS_FOR_CARDS.filter((persona) =>
    selectedCategory === "All" ? true : persona.category === selectedCategory
  );

  const getTitle = () => {
    if (selectedCategory === "All") {
      return "All Personas";
    }
    return `${selectedCategory} Personas`;
  };

  const getSubtitle = () => {
    if (selectedCategory === "All") {
      return "";
    }
    return `Key insights for ${selectedCategory.toLowerCase()} personas`;
  };

  const getCardTitle = (persona: (typeof PERSONAS_FOR_CARDS)[0]) => {
    return `${persona.name} - ${persona.category}`;
  };

  const getCardSubtitle = (persona: (typeof PERSONAS_FOR_CARDS)[0]) => {
    return persona.title;
  };

  // Handle persona card click - opens modal instead of navigating to separate page
  const handlePersonaClick = (id: string) => {
    setSelectedPersonaId(id);
    // Don't call onPersonaClick to avoid navigation - show modal instead
  };

  const closeModal = () => {
    setSelectedPersonaId(null);
  };

  const nextTip = () => setCurrentTip((prev) => (prev + 1) % tips.length);
  const prevPersona = () =>
    setCurrentPersona(
      (prev) => (prev - 1 + personaOverviews.length) % personaOverviews.length
    );
  const nextPersona = () =>
    setCurrentPersona((prev) => (prev + 1) % personaOverviews.length);

  if (filteredPersonas.length === 0) {
    return (
      <div className='space-y-8 p-2'>
        <div className='flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0'>
          {/* Existing header and category buttons */}
          <div>
            <h2 className='text-3xl font-bold text-text-primary'>
              {getTitle()}
            </h2>
            <p className='text-text-secondary mt-2 text-lg'>{getSubtitle()}</p>
          </div>
          <div className='flex space-x-2'>
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                  ${
                    selectedCategory === category
                      ? "bg-blue-600 text-white shadow-md"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        <div className='text-center py-12'>
          <User className='mx-auto h-12 w-12 text-gray-400' />
          <h3 className='mt-4 text-lg font-medium text-text-primary'>
            No personas found
          </h3>
          <p className='mt-2 text-sm text-text-secondary'>
            No personas match the selected category. Try selecting a different
            category.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-8 p-2'>
      <h2 className='text-3xl font-bold text-text-primary mb-6'>
        Customer Personas
      </h2>

      <NeumorphicContainer>
        {/* Tips Section */}
        <div className='neumorphic-elevated mb-8 p-4 rounded-xl flex items-center justify-between relative min-h-[70px]'>
          <span className='absolute -left-4 top-1/2 -translate-y-1/2 neumorphic-elevated rounded-lg px-3 py-1 font-medium text-text-primary shadow-sm'>
            Tip
          </span>
          <div className='flex-1 ml-8'>
            <div className='font-semibold text-text-primary'>
              {tips[currentTip].title}
            </div>
            <div className='text-text-secondary text-sm mt-1'>
              {tips[currentTip].content}
            </div>
          </div>
          <div className='flex flex-col items-end ml-4'>
            <button
              onClick={nextTip}
              className='p-2 rounded-full hover:bg-white/20 transition-colors'
              aria-label='Next Tip'
            >
              <ArrowRight className='h-5 w-5 text-text-muted' />
            </button>
            <span className='text-xs text-text-muted mt-2'>
              {currentTip + 1} of {tips.length}
            </span>
          </div>
        </div>

        {/* Persona Overview Carousel */}
        <PersonaCarouselLayout
          total={personaOverviews.length}
          current={currentPersona}
          onPrev={prevPersona}
          onNext={nextPersona}
          onDotClick={setCurrentPersona}
          personaCategories={personaOverviews.map((persona) => persona.title)}
        >
          {personaOverviews.map((persona, idx) => (
            <div
              key={idx}
              className='flex flex-col items-center justify-center h-full'
            >
              {persona.image && (
                <img
                  src={persona.image}
                  alt={persona.title}
                  className={`object-cover rounded-2xl mb-6 mx-auto transition-all duration-500 ${
                    idx === currentPersona
                      ? "w-32 h-32 shadow-2xl" // Bigger image for selected
                      : "w-20 h-20 shadow-lg" // Smaller image for others
                  }`}
                />
              )}
              {/* Only show text content for the selected persona */}
              {idx === currentPersona && (
                <>
                  <div
                    className={`font-bold text-center mb-4 transition-all duration-500 ${
                      idx === currentPersona
                        ? "text-2xl text-text-primary" // Bigger, darker text for selected
                        : "text-lg text-text-secondary" // Smaller, lighter text for others
                    }`}
                  >
                    {persona.title}
                  </div>
                  <div
                    className={`text-left px-6 transition-all duration-500 ${
                      idx === currentPersona
                        ? "text-base text-text-primary leading-relaxed" // Bigger, more readable for selected
                        : "text-sm text-text-secondary leading-normal" // Smaller for others
                    }`}
                  >
                    {persona.description.length > 200
                      ? `${persona.description.substring(0, 200)}...`
                      : persona.description}
                  </div>
                </>
              )}
            </div>
          ))}
        </PersonaCarouselLayout>
      </NeumorphicContainer>

      <NeumorphicContainer>
        <h2 className='text-3xl font-bold text-text-primary'>{getTitle()}</h2>
        <p className='text-text-secondary mt-2 text-lg'>{getSubtitle()}</p>

        {/* Carousel container with increased padding to prevent hover scaling clipping */}
        {/* - py-8/10/12: Increased vertical padding to accommodate hover:scale-[1.02] effect */}
        {/* - Cards are 500px tall, 2% scale = ~10px growth, so we need extra padding */}
        <div className='px-8 py-8 sm:py-10 lg:py-12 mx-4 sm:mx-8 relative'>
          <Slider {...sliderSettings}>
            {filteredPersonas.map((persona) => (
              <div key={persona.id} className='px-2 py-4'>
                <PersonaCard
                  persona={persona}
                  handlePersonaClick={handlePersonaClick}
                />
              </div>
            ))}
          </Slider>
        </div>
      </NeumorphicContainer>

      {selectedPersonaId && (
        <div
          className='modal-with-sidebar bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center'
          onClick={closeModal}
        >
          {/* Exit Button - Outermost component for easy access */}
          <button
            onClick={closeModal}
            className='absolute top-4 right-4 z-50 neumorphic-elevated p-3 rounded-full text-text-primary hover:text-text-secondary transition-colors shadow-lg'
          >
            <X className='h-6 w-6' />
          </button>

          <div
            className='neumorphic-inset rounded-2xl overflow-hidden w-full max-w-[1200px] p-8 max-h-[90%] overflow-y-auto relative m-4'
            onClick={(e) => e.stopPropagation()}
          >
            <PersonaDetail personaId={selectedPersonaId} />
          </div>
        </div>
      )}
    </div>
  );
}
