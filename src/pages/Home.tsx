import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>R<PERSON>, Users, Clock, BarChart2, Check } from "lucide-react";
import { Videos } from "./Videos";
import { Podcasts } from "./Podcasts";

export function Home() {
  const stats = [
    {
      label: "Interviews",
      value: "24/24",
      change: "+100%",
      changeType: "positive",
    },
    {
      label: "Goals Met",
      value: "8/10",
      change: "+80%",
      changeType: "positive",
    },
    {
      label: "Days Left",
      value: "2",
      change: "On Track",
      changeType: "neutral",
    },
  ];

  const timelineEvents = [
    {
      phase: "Phase III: Holiday Experience",
      date: "2025-06-11",
      completed: true,
    },
    {
      phase: "Phase IV: Depth Interviews",
      date: "2025-06-12",
      completed: true,
    },
    { phase: "Today: Persona Workshop", date: "2025-06-13", current: true },
  ];

  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Research Lead",
      imageUrl:
        "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80&w=200&h=200",
    },
    {
      name: "<PERSON>",
      role: "Senior Analyst",
      imageUrl:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80&w=200&h=200",
    },
    {
      name: "Emily Rodriguez",
      role: "UX Researcher",
      imageUrl:
        "https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&q=80&w=200&h=200",
    },
  ];

  return (
    <div className='max-w-7xl mx-auto'>
      {/* Progress Section */}
      <section className='mb-8'>
        <div className='flex items-center justify-between mb-6'>
          <div>
            <h2 className='text-xl font-bold text-gray-900'>
              Progress Overview
            </h2>
            <p className='text-sm text-gray-500 mt-1'>
              Research project status and metrics
            </p>
          </div>
          <Link
            to='/home/<USER>'
            className='inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700'
          >
            View Details <ArrowRight className='ml-1 h-4 w-4' />
          </Link>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          {stats.map((stat, index) => (
            <div key={index} className='bg-white rounded-xl shadow-sm p-6'>
              <div className='flex items-center justify-between mb-4'>
                <div className='p-2 bg-blue-50 rounded-lg'>
                  {index === 0 ? (
                    <Users className='h-5 w-5 text-blue-600' />
                  ) : index === 1 ? (
                    <BarChart2 className='h-5 w-5 text-blue-600' />
                  ) : (
                    <Clock className='h-5 w-5 text-blue-600' />
                  )}
                </div>
                <span
                  className={`text-sm font-medium px-2.5 py-0.5 rounded-full ${
                    stat.changeType === "positive"
                      ? "bg-green-50 text-green-600"
                      : "bg-gray-50 text-gray-600"
                  }`}
                >
                  {stat.change}
                </span>
              </div>
              <h3 className='text-2xl font-bold text-gray-900 mb-1'>
                {stat.value}
              </h3>
              <p className='text-sm text-gray-500'>{stat.label}</p>
            </div>
          ))}
        </div>
      </section>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8'>
        {/* Timeline Section */}
        <section className='bg-white rounded-xl shadow-sm'>
          <div className='p-6'>
            <div className='flex items-center justify-between mb-6'>
              <div>
                <h2 className='text-xl font-bold text-gray-900'>
                  Recent Timeline
                </h2>
                <p className='text-sm text-gray-500 mt-1'>
                  Latest project milestones
                </p>
              </div>
              <Link
                to='/home/<USER>'
                className='inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700'
              >
                Full Timeline <ArrowRight className='ml-1 h-4 w-4' />
              </Link>
            </div>
            <div className='space-y-4'>
              {timelineEvents.map((event, index) => (
                <div key={index} className='flex items-center space-x-4'>
                  <div
                    className={`h-3 w-3 rounded-full flex-shrink-0 ${
                      event.current
                        ? "bg-blue-500 ring-4 ring-blue-100"
                        : "bg-green-500"
                    }`}
                  >
                    {!event.current && <Check className='h-3 w-3 text-white' />}
                  </div>
                  <div className='flex-1 min-w-0'>
                    <p className='text-sm font-medium text-gray-900 truncate'>
                      {event.phase}
                    </p>
                    <p className='text-sm text-gray-500'>{event.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className='bg-red rounded- shadow-sm'>
          <div className='p-6'>
            <div className='flex items-center justify-between mb-6'>
              <div>
                <h2 className='text-xl font-bold text-gray-900'>
                  Research Team
                </h2>
                <p className='text-sm text-gray-500 mt-1'>Key team members</p>
              </div>
              <Link
                to='/home/<USER>'
                className='inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700'
              >
                Meet the Team <ArrowRight className='ml-1 h-4 w-4' />
              </Link>
            </div>
            <div className='space-y-4'>
              {teamMembers.map((member, index) => (
                <div key={index} className='flex items-center space-x-4'>
                  <img
                    src={member.imageUrl}
                    alt={member.name}
                    className='w-10 h-10 rounded-full object-cover'
                  />
                  <div>
                    <h3 className='text-sm font-medium text-gray-900'>
                      {member.name}
                    </h3>
                    <p className='text-xs text-gray-500'>{member.role}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>

      {/* Videos and Podcasts sections */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
        <Videos />
        <Podcasts />
      </div>

      {/* Background Image Credit */}
      <div className='mt-8 pt-6 border-t border-gray-200'>
        <p className='text-xs text-gray-500 text-center'>
          Background image by{" "}
          <a
            href='https://unsplash.com/@martz90'
            target='_blank'
            rel='noopener noreferrer'
            className='text-blue-600 hover:text-blue-700 underline'
          >
            Martin Martz
          </a>
          {" "}on Unsplash
        </p>
      </div>
    </div>
  );
}
