import React from "react";
import { NeumorphicContainer } from "../components/NeumorphicContainer";

const timelineEvents = [
  {
    phase: "Study Design & Participant Recruit",
    date: "Jun 05, 2025",
    completed: true,
    details: [
      "Research methodology defined",
      "Participant criteria established",
      "30 potential participants identified",
    ],
  },
  {
    phase: "Phase I: Real Life",
    date: "Jun 07, 2025",
    completed: true,
    details: [
      "In-depth interviews conducted",
      "Daily activity logs collected",
      "Behavioral patterns analyzed",
    ],
  },
  {
    phase: "Phase II: Holiday Prep",
    date: "Jun 09, 2025",
    completed: true,
    details: [
      "Shopping patterns documented",
      "Decision-making processes analyzed",
      "Pain points identified",
    ],
  },
  {
    phase: "Phase III: Holiday Experience",
    date: "Jun 11, 2025",
    completed: true,
    details: [
      "Experience mapping completed",
      "Key moments identified",
      "Satisfaction metrics collected",
    ],
  },
  {
    phase: "Phase IV: Depth Interviews",
    date: "Jun 12, 2025",
    completed: true,
    details: [
      "Follow-up interviews conducted",
      "Data synthesis completed",
      "Initial insights generated",
    ],
  },
  {
    phase: "Persona Workshop",
    date: "Today",
    current: true,
    details: [
      "Persona attributes definition",
      "Journey mapping",
      "Validation session",
    ],
  },
  {
    phase: "Completion",
    date: "Jun 15, 2025",
    upcoming: true,
    details: [
      "Final report compilation",
      "Presentation preparation",
      "Stakeholder review",
    ],
  },
];
export function Progress() {
  const completedCount = timelineEvents.filter((te) => te.completed).length;
  const progressPercentage = (completedCount / timelineEvents.length) * 100;
  return (
    <div className='space-y-8 p-2'>
      {/* <div>
        <h2 className="text-3xl font-bold text-gray-900">Progress Overview</h2>
        {/* <p className="text-gray-500 mt-2 text-lg">Current project status</p> * /}
      </div> */}

      {/* <section className="bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-xl p-14 border border-gray-100">
        <div className="prose prose-gray max-w-none">
          {/* <h3 className="text-2xl font-semibold text-gray-900 mt-0">Completion: 70%</h3> * /}
          <p className="text-gray-600">
            We are currently at 70% completion of the project. The major milestones achieved so far include:
          </p>
          <ul className="text-gray-600">
            <li>Persona research and data collection</li>
            <li>Initial persona creation and validation</li>
            <li>Persona Immersion & Activation Workshop</li>
            <li>Development of key insights and strategies</li>
          </ul>
          <p className="text-gray-600">
            The remaining tasks include finalizing the persona profiles, integrating feedback from the workshop, and preparing the final presentation and documentation.
          </p>
        </div>
      </section> */}

      <div>
        <div className='flex items-center gap-4'>
          <h2 className='text-3xl font-bold text-text-primary'>
            Current Status
          </h2>
          <span className='text-3xl font-medium text-text-muted'>
            latest update as of July 02, 2025
          </span>
        </div>
        <p className='text-text-secondary mt-2 text-lg'>Completion: 71%</p>
      </div>

      <NeumorphicContainer>
        <div className='flex items-center gap-2'>
          <div className='h-2 flex-1 rounded-full bg-gray-200'>
            <div
              className='h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-500'
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <span className='text-sm font-medium text-text-secondary'>
            {Math.round(progressPercentage)}%
          </span>
        </div>

        <div className='prose prose-gray max-w-none relative'>
          <p className='text-lg text-text-primary leading-relaxed'>
            We are completing the on-premise Persona Immersion & Activation
            Workshop.
          </p>

          <br />

          <p className='text-text-secondary'>
            The goal of this workshop is to introduce the Personas & their
            common characteristics and then go through next steps required
            before completion.
          </p>
        </div>
      </NeumorphicContainer>
    </div>
  );
}
