# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Scoot Insights is a comprehensive marketing insights platform built with React + TypeScript frontend and Node.js + Express backend. The application provides persona management, brand/client management, file storage with S3 integration, and PIN-based authentication for admin operations.

## Development Commands

### Frontend (Root Directory)
- `npm run dev` - Start Vite development server (port 5173)
- `npm run build` - Build for production (outputs to `build/` directory)
- `npm run lint` - Run ESLint for code linting
- `npm run preview` - Preview production build

### Backend (server/ Directory)
- `cd server && npm run dev` - Start development server with hot reload (port 3000)
- `cd server && npm run build` - Build TypeScript to JavaScript
- `cd server && npm run start` - Start production server
- `cd server && npm run lint` - Run ESLint for backend code
- `cd server && npm run test` - Run Jest tests
- `cd server && npm run migrate:files` - Run database migrations
- `cd server && npm run test:db` - Test database connection

### Development Setup
Both frontend and backend must run simultaneously:
```bash
# Terminal 1: Backend
cd server && npm run dev

# Terminal 2: Frontend  
npm run dev
```

## Architecture Overview

### Full-Stack Application Structure
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript + Sequelize ORM
- **Database**: PostgreSQL with UUID primary keys
- **Storage**: AWS S3 for file uploads (logos, images)
- **Authentication**: PIN-based admin authentication stored in sessionStorage

### Key Architectural Patterns

#### Brand-Based Routing
The application uses dynamic brand-based URLs:
- Root path `/` redirects to default brand: `/{defaultBrand}/overview`
- Brand routes: `/{brandName}/overview`, `/{brandName}/personas`, etc.
- Admin route: `/admin` (requires PIN authentication)

#### API-First Architecture
- All data operations go through REST API endpoints
- Frontend services (brandApiService, clientApiService) handle API communication
- No localStorage for business data - only tour state and client config

#### Modal System with Smart Scroll Management
- Context-based modal system with `ModalProvider`
- Smart scroll management that differentiates between regular modals and tooltips
- TourGuide allows background scrolling, other modals prevent it

#### Authentication Flow
1. Admin enters PIN on `/admin` route
2. PIN stored in sessionStorage and sent via `X-Admin-Pin` header
3. Backend middleware validates PIN for destructive operations
4. Read operations remain public, write/delete operations require authentication

### Component Architecture

#### Layout System
- `Layout.tsx` - Main application layout with brand loading and routing
- `Header.tsx` - Fixed header with brand information
- `Sidebar.tsx` - Collapsible navigation sidebar
- `App.tsx` - Root component with routing and authentication

#### Page Structure
- **Overview Pages**: Progress, Timeline, Team, PersonaList, Media
- **Detail Pages**: PersonaDetail (dynamic persona viewing)
- **Admin Pages**: Admin (PIN-protected), ClientAdmin (client/brand management)

#### Service Layer
- **API Services**: brandApiService, clientApiService, uploadService
- **Utility Services**: tourService, authService, personaService
- **Core Services**: apiClient (axios instance with interceptors)

### Database Schema

#### Core Tables
- **clients**: Client information with soft delete (`enabled` flag)
- **brands**: Brands belonging to clients with unique endpoints
- **files**: S3 file metadata with organized folder structure
- **admin_pins**: PIN-based authentication (default: ADMIN1)

#### Relationships
- Brands belong to Clients (Many-to-One)
- Files are organized by folder (logos/client, logos/brand, images/)

### File Storage System
- **S3 Integration**: Automatic upload with image optimization via Sharp
- **Folder Structure**: `logos/client/`, `logos/brand/`, `images/personas/`
- **Security**: File type validation, size limits (10MB), virus scanning
- **Optimization**: Automatic image resizing and quality optimization

## Important Implementation Details

### Design System & CSS Modules
The project uses CSS Modules alongside a comprehensive design system:
- **CSS Modules**: Component-specific styles in `*.module.css` files for scoped styling
- **Design tokens**: Shared CSS custom properties in `src/styles/design-tokens.module.css`
- **Component classes**: Modern CSS modules in `src/components/[Component].module.css`
- **Legacy support**: Global styles in `src/styles/components.css` (being phased out)
- **Tailwind integration**: Still available for utility classes when needed

**CSS Modules Pattern**:
```tsx
import styles from './Component.module.css';

// Use scoped class names
<div className={styles.container}>
  <button className={`${styles.btn} ${styles.primary}`}>
    Click me
  </button>
</div>
```

**Key Benefits**:
- Scoped styles prevent class name conflicts
- Better maintainability with co-located styles
- Type safety with TypeScript integration
- Automatic dead code elimination

### State Management Patterns
- **Brand State**: Loaded from URL params and API, managed in Layout component
- **Authentication**: Stored in sessionStorage, managed by auth utilities
- **Modal State**: Context-based with useModal hook and ModalProvider
- **Tour State**: localStorage for completion tracking

### API Request Patterns
- All API requests automatically include admin PIN header when authenticated
- Error handling with user-friendly messages and proper HTTP status codes
- Request/response validation using express-validator middleware

### File Upload Flow
1. Frontend validates file (type, size) before upload
2. Backend validates again and scans for security issues
3. Image optimization and resizing via Sharp
4. Upload to S3 with organized folder structure
5. Database record creation with file metadata
6. Return public S3 URL to frontend

### Authentication Security
- PINs are 6-character alphanumeric, case-insensitive
- Transmitted via HTTP headers, not URL params or body
- Session-based (no expiration until logout)
- Protects all destructive operations (POST, PUT, DELETE)
- Read operations remain public for performance

## Common Development Tasks

### Adding New API Endpoints
1. Create route in `server/src/routes/`
2. Add validation middleware if needed
3. Add authentication middleware for protected routes
4. Create corresponding service function if complex logic
5. Update API documentation in `README/BACKEND_API_DOCUMENTATION.md`

### Creating New Components
1. Create both `Component.tsx` and `Component.module.css` files
2. Use CSS modules for scoped styling with design tokens
3. Import and use context providers (ModalProvider) when needed
4. Follow naming conventions (PascalCase for components, camelCase for CSS classes)

**CSS Modules Example**:
```tsx
// Component.tsx
import styles from './Component.module.css';

export function Component({ variant, size }) {
  return (
    <div className={`${styles.component} ${styles[variant]} ${styles[size]}`}>
      Content
    </div>
  );
}
```

```css
/* Component.module.css */
.component {
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
}

.primary {
  background: var(--color-primary-600);
  color: white;
}

.large {
  padding: var(--spacing-6);
  font-size: var(--font-size-lg);
}
```

### Working with File Uploads
1. Use existing `uploadService.ts` for file operations
2. Follow S3 folder conventions: `logos/client`, `logos/brand`, `images/{category}`
3. Always validate file types and sizes on both frontend and backend
4. Handle upload errors gracefully with user feedback

### Database Migrations
- Migration scripts in `server/src/migrations/`
- Run via `npm run migrate:files` in server directory
- Always test migrations with `npm run test:db`

## Environment Configuration

### Required Environment Variables
**Backend (.env in server/ directory)**:
```
DB_HOST=localhost
DB_PORT=5432  
DB_NAME=scoot_insights
DB_USER=postgres
DB_PASSWORD=your_password
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_BUCKET=your_bucket_name
AWS_REGION=us-east-1
PORT=3000
NODE_ENV=development
CORS_ORIGIN=http://localhost:5173
```

### Default Admin PIN
The default admin PIN is `ADMIN1` - used for accessing protected admin functions.

## Testing and Quality

### Linting
- Both frontend and backend use ESLint with TypeScript configs
- Run `npm run lint` in respective directories
- Fix issues before committing changes

### Type Safety  
- Full TypeScript implementation across frontend and backend
- Shared type definitions where applicable
- Strict mode enabled for better type checking

## Important Notes

- **Brand Loading**: Always handle async brand loading states in components
- **Error Boundaries**: Implement proper error handling for API failures  
- **File Uploads**: Always validate file types and sizes before and after upload
- **Authentication**: Check authentication state before allowing destructive operations
- **Responsive Design**: Components should work on mobile and desktop
- **Performance**: Use React.memo and useMemo for expensive operations when needed

## Documentation References

- `README.md` - Comprehensive setup and feature documentation
- `STYLING_GUIDE.md` - Complete design system documentation
- `AUTHENTICATION_IMPLEMENTATION.md` - Authentication system details
- `README/BACKEND_API_DOCUMENTATION.md` - Complete API reference
- `README/S3_SETUP.md` - S3 configuration guide
- `server/DEPLOYMENT.md` - Deployment instructions