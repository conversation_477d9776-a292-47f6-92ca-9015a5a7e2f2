# Scoot Insights - Main Documentation

A comprehensive marketing insights platform with persona management, brand/client management, and file storage capabilities.

> **Note**: This is the main project documentation. All documentation files have been centralized in the `/README` folder for better organization.

## 🚀 Features

### Core Features

- **Persona Management**: Create and manage detailed customer personas with rich metadata
- **Brand & Client Management**: Organize brands under clients with logo management
- **S3 File Storage**: Secure, scalable file storage for logos and images
- **Admin Interface**: Full CRUD operations for all entities
- **Responsive Design**: Modern, mobile-friendly UI with neumorphic design

### S3 Integration Features

- ✅ **Automatic Logo Upload**: Upload logos directly to S3 when creating/editing brands/clients
- ✅ **Logo Replacement**: Replace existing logos with automatic cleanup of old files
- ✅ **Logo Deletion**: Automatic S3 cleanup when brands/clients are deleted
- ✅ **Image Optimization**: Automatic image resizing and optimization using Sharp
- ✅ **Error Handling**: Comprehensive error handling with user-friendly messages
- ✅ **Connection Testing**: Built-in S3 connection testing and status monitoring
- ✅ **File Validation**: Virus scanning and file type validation
- ✅ **Organized Storage**: Files organized by type (logos/client, logos/brand, images/)

## 🛠️ Tech Stack

### Frontend

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **Axios** for API communication
- **API-First Architecture**: All data operations go through backend API endpoints

## 🔄 Recent Updates

### Page-Based Navigation Implementation (Latest)

The application now has a page-based navigation structure where Overview and Admin are separate pages:

- ✅ **Overview Page**: Clicking "Overview" navigates to a dedicated page with all overview sections (Progress, Timeline, Team, Personas, Media)
- ✅ **Admin Page**: Clicking "Admin" navigates to a dedicated page with all admin sections (AI Chatbot, Team Management, Workflow, Auto-Reports, Analytics)
- ✅ **Separate Navigation**: Each page contains all its respective sections, making navigation clearer
- ✅ **Consistent Styling**: Both pages follow the same styling patterns
- ✅ **Scroll Navigation**: Sections scroll within their respective pages
- ✅ **Placeholder Content**: Admin sections include detailed placeholder content explaining their purpose
- ✅ **Client-Focused**: Admin tab is for client/brand management (different from developer admin)

**Technical Implementation:**

- **New ClientAdmin Component**: Created `src/pages/ClientAdmin.tsx` with 5 admin sections
- **Updated Sidebar**: Added Admin expandable section with navigation to admin page
- **Updated Layout**: Modified to handle overview and admin as separate page views
- **Icon Integration**: Added new Lucide icons for admin sections
- **State Management**: Added admin section expansion state management
- **Route Handling**: Overview and Admin pages have separate routes (`/overview`, `/admin`)
- **Navigation Logic**: Section headers navigate to their respective pages, sub-items navigate to specific sections

### Navigation Restructuring

The application now has a unified Overview section with all main features:

- ✅ **Unified Overview Section**: All main features (Progress, Timeline, Team, Personas, Media) are now under the Overview navigation
- ✅ **Consistent Navigation**: Single expandable Overview section with all sub-items
- ✅ **Preserved Functionality**: All existing components and functionality remain unchanged
- ✅ **Simple Routes**: Maintains simple routes like `/personas`, `/media` for consistency
- ✅ **Updated Layout**: All sections render together in the main view
- ✅ **Clean Architecture**: No changes to existing components, just navigation restructuring

**Technical Implementation:**

- **Updated Sidebar**: Moved Personas and Media under Overview section as sub-items
- **Updated Layout**: All sections (Progress, Timeline, Team, Personas, Media) render in main view
- **Preserved Routes**: Routes remain simple and consistent (`/personas`, `/media`, etc.)
- **Updated Navigation Logic**: Layout component handles all sections under Overview
- **Preserved Components**: All existing components remain unchanged internally
- **Tour System**: Tour steps remain the same since functionality is unchanged

### Smart Scroll Position Management

The application now intelligently manages scroll behavior for different types of modals:

- ✅ **Automatic modal type detection**: Distinguishes between regular modals and tooltips
- ✅ **Regular modals**: Prevent background scrolling and preserve exact scroll position
- ✅ **Tooltip modals**: Allow background scrolling for navigation (TourGuide)
- ✅ **Sidebar navigation support**: TourGuide can scroll to highlight sidebar nav options
- ✅ **Multiple modal support**: Handles multiple modals with proper state management
- ✅ **Universal implementation**: All modal components automatically use the smart scroll manager

**Technical Features:**

- **Smart ScrollManager utility**: Automatically detects modal types based on component names
- **Component-based detection**: TourGuide is treated as tooltip, all others as regular modals
- **Sidebar navigation logic**: Special handling for TourGuide when targeting sidebar elements
- **Unique modal IDs**: Each modal instance gets a unique identifier using `useId()`
- **Body style preservation**: Original body styles are preserved and restored
- **React hooks integration**: Easy-to-use `useScrollManager(modalId, isOpen, componentName)` hook
- **TypeScript support**: Fully typed implementation

### Authentication Implementation

The application now has PIN-based authentication for all destructive operations:

- ✅ **Added authentication middleware**: Protects all create, update, and delete operations
- ✅ **PIN-based authentication**: Admin must enter PIN for destructive operations
- ✅ **Session persistence**: Authentication lasts until admin logout
- ✅ **Automatic header injection**: Frontend automatically includes PIN in API requests
- ✅ **Protected routes**: Brands, clients, and file uploads require authentication

**Security Features:**

- **PIN validation**: 6-character alphanumeric PINs stored in database
- **Case insensitive**: PINs are converted to uppercase for comparison
- **No expiration**: Authentication persists until logout
- **Secure transmission**: PINs sent via HTTP headers

### localStorage Cleanup

The application has been migrated from a hybrid localStorage/API architecture to a pure API-first approach:

- ✅ **Removed localStorage-based services**: Eliminated `brandService.ts` and `clientService.ts`
- ✅ **Updated components**: All components now use API services (`brandApiService`, `clientApiService`)
- ✅ **Removed migration service**: No longer needed as data is fully migrated to database
- ✅ **Kept essential localStorage**: Only tour status and client configuration remain in localStorage
- ✅ **Created utility functions**: Moved file validation functions to `utils/fileUtils.ts`

**Benefits:**

- **Data consistency**: Single source of truth (database)
- **Real-time updates**: All users see the same data
- **Better scalability**: No client-side data storage limitations
- **Improved security**: Data validation and access control on server

### Backend

- **Node.js** with TypeScript
- **Express.js** for API server
- **Sequelize** ORM with PostgreSQL
- **AWS S3** for file storage
- **Sharp** for image optimization
- **Multer** for file upload handling

### Database

- **PostgreSQL** for data persistence
- **UUID** primary keys for security
- **Proper indexing** for performance

## 📦 Installation

### Prerequisites

- Node.js 18+
- PostgreSQL 12+
- AWS Account (for S3)

### 1. Clone the Repository

```bash
git clone <repository-url>
cd scoot-insights
```

### 2. Install Dependencies

```bash
# Install frontend dependencies
npm install

# Install backend dependencies
cd server
npm install
```

### 3. Database Setup

```bash
# Create PostgreSQL database
createdb scoot_insights

# Run migrations
npm run migrate

# Seed initial data
npm run seed
```

### 4. S3 Setup

Follow the detailed guide in `README/S3_SETUP.md` to configure AWS S3 for file storage.

### 5. Environment Configuration

```bash
# Copy environment template
cp server/env.example server/.env

# Edit with your configuration
nano server/.env
```

Required environment variables:

```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=scoot_insights
DB_USER=postgres
DB_PASSWORD=your_password

# AWS S3 (see README/S3_SETUP.md)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name

# Server
PORT=3000
NODE_ENV=development
CORS_ORIGIN=http://localhost:5173
```

### 6. Start Development Servers

```bash
# Terminal 1: Start backend
cd server
npm run dev

# Terminal 2: Start frontend
npm run dev
```

## 🔧 Development

### Available Scripts

#### Frontend

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

#### Backend

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript
npm run start        # Start production server
npm run migrate      # Run database migrations
npm run seed         # Seed initial data
npm run reset        # Reset database and reseed
```

### API Endpoints

#### Authentication

- `POST /admin/login` - Admin login with PIN

#### Brands

- `GET /brands` - Get all brands
- `GET /brands/:id` - Get brand by ID
- `POST /brands` - Create new brand
- `PUT /brands/:id` - Update brand
- `DELETE /brands/:id` - Delete brand

#### Clients

- `GET /clients` - Get all clients
- `GET /clients/:id` - Get client by ID
- `POST /clients` - Create new client
- `PUT /clients/:id` - Update client
- `DELETE /clients/:id` - Delete client

#### File Upload

- `POST /upload/logo` - Upload logo file
- `PUT /upload/logo/replace` - Replace existing logo
- `DELETE /upload/logo` - Delete logo file
- `GET /upload/status` - Check S3 connection status

### Database Schema

#### Brands Table

- `id` (UUID, Primary Key)
- `name` (String) - Brand name
- `logo` (Text) - S3 URL or placeholder URL
- `endpoint` (String, Unique) - URL-friendly identifier
- `client_id` (UUID, Foreign Key) - Associated client
- `is_default` (Boolean) - Default brand flag
- `created_at` (Timestamp)
- `updated_at` (Timestamp)

#### Clients Table

- `id` (UUID, Primary Key)
- `name` (String, Unique) - Client name
- `logo` (Text) - S3 URL or placeholder URL
- `description` (Text) - Client description
- `enabled` (Boolean) - Active status
- `created_at` (Timestamp)
- `updated_at` (Timestamp)

#### Files Table

- `id` (UUID, Primary Key)
- `original_name` (String) - Original filename
- `file_name` (String) - Generated unique filename
- `file_key` (String) - S3 object key
- `file_url` (String) - Full S3 URL
- `file_size` (BigInt) - File size in bytes
- `mime_type` (String) - MIME type
- `folder` (String) - S3 folder path
- `uploaded_by` (String) - User identifier
- `created_at` (Timestamp)
- `updated_at` (Timestamp)

## 🔒 Security Features

- **Input Validation**: Comprehensive validation for all inputs
- **File Type Validation**: Only allowed image types (JPEG, PNG, GIF, WebP)
- **File Size Limits**: 10MB maximum file size
- **Virus Scanning**: Basic executable and script detection
- **CORS Protection**: Configurable CORS origins
- **Helmet.js**: Security headers
- **UUID Primary Keys**: Non-sequential IDs for security

## 📁 File Storage

### S3 Organization

```
your-bucket/
├── logos/
│   ├── client/          # Client logos
│   └── brand/           # Brand logos
└── images/
    ├── personas/        # Persona images
    └── general/         # General images
```

### Features

- **Automatic Optimization**: Images resized and optimized using Sharp
- **Public Access**: Files served directly from S3 for performance
- **Automatic Cleanup**: Old files deleted when replaced
- **Error Handling**: Graceful handling of S3 errors
- **Status Monitoring**: Real-time S3 connection status

## 🚀 Deployment

### Production Build

```bash
# Frontend
npm run build

# Backend
cd server
npm run build
```

### Environment Variables

Set production environment variables:

- `NODE_ENV=production`
- `CORS_ORIGIN` with your domain
- Production database credentials
- AWS S3 credentials

### Database Migration

```bash
cd server
npm run migrate
```

## 🐛 Troubleshooting

### Common Issues

1. **S3 Connection Failed**

   - Check AWS credentials in `.env`
   - Verify bucket name and region
   - Ensure IAM user has correct permissions

2. **File Upload Errors**

   - Check file size (max 10MB)
   - Verify file type (JPEG, PNG, GIF, WebP)
   - Check S3 bucket permissions

3. **Database Connection Issues**

   - Verify PostgreSQL is running
   - Check database credentials
   - Ensure database exists

4. **CORS Errors**
   - Check `CORS_ORIGIN` in environment
   - Verify frontend URL is included

### Debug Mode

Set `NODE_ENV=development` for detailed error messages and logging.

## 📝 API Documentation

Complete API documentation is available in `README/BACKEND_API_DOCUMENTATION.md`.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:

1. Check the troubleshooting section
2. Review the S3 setup guide
3. Check the API documentation
4. Open an issue on GitHub
